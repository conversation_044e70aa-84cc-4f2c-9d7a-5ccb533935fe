import cryto from 'node:crypto'

export function toSeconds(milliseconds: number) {
    return Math.floor(milliseconds / 1000);
}

export function toSecondsDateString(dateString: string) {
    return Date.parse(dateString) / 1000;
}

export const randomGID = () => {
    return cryto.randomUUID().replace(/-/g, "")
}
//random string
export const randomString = (length: number) => {
    let result = '';
    const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    const charactersLength = characters.length;
    for (let i = 0; i < length; i++) {
        result += characters.charAt(Math.floor(Math.random() * charactersLength));
    }
    return result;
}
export const money = (number: any) => {
    if (number) {
        // kiểm tra số 0 ở đâu thì xoá
        number = number?.toString().replace(/^0+/, '');
        if (typeof number === 'string') {
            return number.replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,');
        } else if (!isNaN(number)) {
            number = number.toFixed(2);
            return number.toString().replace('.00', '').replace(/\B(?=(\d{3})+(?!\d))/g, ',');
        }
    }
    return 0;
}
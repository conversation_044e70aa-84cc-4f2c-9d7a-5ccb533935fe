import crudDA from "../da/crudDA";
import { pid } from "../config";
import { randomGID, randomString } from "../Ultis/convert";
import e from "express";

export default class Reward {
    private _customer = new crudDA(`data:${pid}:${"Customer"}`);
    private _orderDetail = new crudDA(`data:${pid}:${"OrderDetail"}`);
    private _product = new crudDA(`data:${pid}:${"Product"}`);
    private _shopcate = new crudDA(`data:${pid}:${"ShopCate"}`);
    private _shopReward = new crudDA(`data:${pid}:${"ShopReward"}`);
    private _reward = new crudDA(`data:${pid}:${"Reward"}`);
    private _historyReward = new crudDA(`data:${pid}:${"HistoryReward"}`);
    async fnFilial(shopid: string, orderid: string, customerId: string, status: number, code: string): Promise<any> {
        console.log('---testttt', shopid, orderid, customerId, status, code);
        try {
            const rw = await this._historyReward.search(1, 1000, `@OrderId:{${orderid}}`,
                {
                    RETURN: ["Id"],
                    SORTBY: {
                        "BY": "DateCreated",
                        "DIRECTION": "DESC"
                    }
                }) as any;
            console.log('---rw', rw);
            if (!rw.length && status == 1) {
                const init = await Promise.all([
                    this._customer.search(1, 1, `@Id:{${customerId}}`, {
                        RETURN: ["ListParent"], SORTBY: {
                            "BY": "DateCreated",
                            "DIRECTION": "DESC"
                        }
                    }),
                    this._orderDetail.search(1, 100, `@OrderId:{${orderid}}`,
                        {
                            RETURN: ["Id", "ProductId", "Quantity", "Price", "Total"],
                            SORTBY: {
                                "BY": "DateCreated",
                                "DIRECTION": "DESC"
                            }
                        }),
                    this._shopcate.search(1, 100, `@ShopId:{${shopid}} @Status:[1]`, {
                        RETURN: ['Id', "CategoryId", "DateCreated", 'Percent', 'Filial'], SORTBY: {
                            "BY": "DateCreated",
                            "DIRECTION": "DESC"
                        }
                    }),

                    this._shopReward.search(1, 100, `@ShopId:{${shopid}} @Status:[1]`, {
                        RETURN: ['Id', "CategoryId", 'Percent', "DateCreated", 'Filial'], SORTBY: {
                            "BY": "DateCreated",
                            "DIRECTION": "DESC"
                        }
                    }),
                    this._reward.search(1, 100, `*`, {
                        RETURN: ['Id', 'Percent', 'Filial'], SORTBY: {
                            "BY": "DateCreated",
                            "DIRECTION": "DESC"
                        }
                    })
                ]);
                const ListP = init[0].data?.[0] as any;
                const ListParent = ListP.ListParent?.split(",") ?? [];
                ListParent.unshift(customerId);
                const listp = init[1].data as any
                const listcate = init[2].data as any;
                const Listshop = init[3].data as any;
                const ListSys = init[4].data as any;
                let listProduct = [] as any;
                let list = [] as any;
                if (listcate.length) {
                    let pkey = `@ShopId:{${shopid}}  @Id:{${listp.map((e: any) =>
                        e.ProductId).filter((id: string, i: number, arr: Array<any>) => arr.indexOf(id) === i).join(" | ")}}`;
                    pkey += ` @CategoryId:{${listcate.map((e: any) =>
                        e.CategoryId).filter((id: string, i: number, arr: Array<any>) => arr.indexOf(id) === i).join(" | ")}}`;
                    console.log('---pkey', pkey);
                    // Product By ShopCate
                    listProduct = (await this._product.search(1, 100, pkey, {
                        RETURN: ["Id", "CategoryId"], SORTBY: {
                            "BY": "DateCreated",
                            "DIRECTION": "DESC"
                        }
                    }) as any).data;
                    var listpbyCate = [] as any;
                    listp.forEach((e: any) => {
                        if(listProduct?.some((m: any) => m.Id == e.ProductId))
                        {
                            listpbyCate.push({...e, CategoryId: listProduct.find((m: any) => m.Id == e.ProductId)?.CategoryId });
                        }
                    })
                    console.log('---listpbyCate', listpbyCate.length);
                    console.log('---listProduct', listProduct);
                    console.log('---listcate', listcate);
                    console.log('---ListParent', ListParent);  

                    if (listpbyCate.length ) {
                        if (listcate.length) {
                            listpbyCate.forEach((e: any) => {
                                listcate.filter((t: any) => t.CategoryId == e.CategoryId).forEach((t: any) => {
                                    console.log('---t', t);
                                    console.log('---e', e);
                                    if (t.Filial == 0) {
                                        list.push({
                                            Id: randomGID(),
                                            Filial: t.Filial * 1,
                                            OrderDetailId: e.Id,
                                            CustomerId: customerId,
                                            Value: e.Total * t.Percent / 100,
                                            ShopCateId: t.Id,
                                            Status: 1,
                                            OrderId: orderid,
                                            Type: 1,//hoa hồng
                                            Name: `Chuyên mục bản cập nhật: ${t.DateCreated} của đơn hàng ${customerId}`,
                                            DateCreated: Date.now(),
                                            Description: `Thưởng hoa hồng từ đơn hàng #${code}`,
                                            Code: randomString(10)

                                        });
                                    } else if (ListParent[t.Filial]) {
                                        list.push({
                                            Id: randomGID(),
                                            Filial: t.Filial * 1,
                                            OrderDetailId: e.Id,
                                            CustomerId: ListParent[t.Filial],
                                            Value: e.Total * t.Percent / 100,
                                            ShopCateId: t.Id,
                                            Status: 1,
                                            OrderId: orderid,
                                            Type: 1,//hoa hồng
                                            Name: `Chuyên mục bản cập nhật: ${t.DateCreated} của đơn hàng ${customerId}`,
                                            DateCreated: Date.now(),
                                            Description: `Thưởng hoa hồng từ đơn hàng #${code}`,
                                            Code: randomString(10)
                                        });
                                    }
                                })
                            });
                        }
                    }
                }
                var listpbyshop = listp.filter((e: any) => listProduct?.every((m: any) => m.Id !== e.ProductId));
                // ShopCate
                if (listpbyshop.length) {
                    if (Listshop.length) {
                        listpbyshop.forEach((e: any) => {
                            Listshop.forEach((t: any) => {
                                if (t.Filial == 0) {
                                    list.push({
                                    Id: randomGID(),
                                    Filial: t.Filial * 1,
                                    OrderDetailId: e.Id,
                                    CustomerId: customerId,
                                    Value: e.Total * t.Percent / 100,
                                    ShopRewardId: t.Id,
                                    Status: 1,
                                    Type: 1,//hoa hồng
                                    OrderId: orderid,
                                    Name: `Shop ${shopid} bản cập nhật: ${t.DateCreated} của đơn hàng ${customerId}`,
                                    DateCreated: Date.now(),
                                    Description: `Thưởng hoa hồng từ đơn hàng #${code}`,
                                    Code: randomString(10)
                                });
                                }else if (ListParent[t.Filial]) {
                                    list.push({
                                    Id: randomGID(),
                                    Filial: t.Filial * 1,
                                    OrderDetailId: e.Id,
                                    CustomerId: ListParent[t.Filial],
                                    Value: e.Total * t.Percent / 100,
                                    ShopRewardId: t.Id,
                                    Status: 1,
                                    Type: 1,//hoa hồng
                                    OrderId: orderid,
                                    Name: `Shop ${shopid} bản cập nhật: ${t.DateCreated} của đơn hàng ${customerId}`,
                                    DateCreated: Date.now(),
                                    Description: `Thưởng hoa hồng từ đơn hàng #${code}`,
                                    Code: randomString(10)
                                });
                                }
                                
                            })
                        });
                    }
                    // System
                    else {
                        if (ListSys.length) {
                            listpbyshop.forEach((e: any) => {
                                ListSys.forEach((t: any) => {
                                    if (t.Filial == 0) {
                                        list.push({
                                            Id: randomGID(),
                                            Filial: t.Filial * 1,
                                            OrderDetailId: e.Id,
                                            CustomerId: customerId,
                                            Value: e.Total * t.Percent / 100,
                                            RewardId: t.Id,
                                            Status: 1,
                                            Type: 1, //hoa hồng
                                            OrderId: orderid,
                                            Name: `System của đơn hàng ${customerId}`,
                                            DateCreated: Date.now(),
                                            Description: `Thưởng hoa hồng từ đơn hàng #${code}`,
                                            Code: randomString(10)
                                        });
                                    } else if (ListParent[t.Filial]) {
                                        list.push({
                                            Id: randomGID(),
                                            Filial: t.Filial * 1,
                                            OrderDetailId: e.Id,
                                            CustomerId: ListParent[t.Filial],
                                            Value: e.Total * t.Percent / 100,
                                            RewardId: t.Id,
                                            Status: 1,
                                            Type: 1, //hoa hồng
                                            OrderId: orderid,
                                            Name: `System của đơn hàng ${customerId}`,
                                            DateCreated: Date.now(),
                                            Description: `Thưởng hoa hồng từ đơn hàng #${code}`,
                                            Code: randomString(10)
                                        });
                                    }                                    
                                })
                            });
                        }

                    }
                }
                this._historyReward.action("add", list);
            }
            else {
                var list = [] as any;
                if (status == 3) {
                    rw.data?.map((e: any) =>
                        list.push({
                            Id: e.Id,
                            Status: 2
                        }))
                }
                if (status == 4) {
                    rw.data?.map((e: any) =>
                        list.push({
                            Id: e.Id,
                            Status: 3
                        }))
                }
                if (list.length > 0) this._historyReward.action("edit", list);
            }

        } catch (error) {
            console.log('---err', error);
        }

    }
}
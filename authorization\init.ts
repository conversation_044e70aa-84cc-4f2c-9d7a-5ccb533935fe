import crudDA from '../da/crudDA';
import { AggregateGroupByReducers, AggregateSteps } from "redis";

export async function initData({ repo }: { repo: string }) {
    // const relativeSchema = new Schema(`setting:${repo}:relative`, {
    //     Id: { type: "string", path: '$.Id' },
    //     TABLE_PK: { type: "text", path: '$.TABLE_PK' }, //center
    //     TABLE_FK: { type: "text", path: '$.TABLE_FK' }, //class trong bảng centerid
    //     COLUMN_FK: { type: "text", path: '$.COLUMN_FK' }, //centerid
    //     FE: { type: "string", path: '$.FE' },
    //     Redis: { type: "string", path: '$.Redis' },
    // }, {
    //     dataStructure: 'JSON',
    // })
    // const relativeRepo = new crudDA(relativeSchema);
    // switch (repo) {
    //     case 'wini':
    //         break;
    //     case 'setting':
    //         const settingAllColSchema = new Schema(`setting`, {
    //             Id: { type: "string", path: '$.Id' },
    //             TABLE_CATALOG: { type: "text", path: '$.TABLE_CATALOG' },
    //             TABLE_NAME: { type: "text", path: '$.TABLE_NAME' },
    //             COLUMN_NAME: { type: "text", path: '$.COLUMN_NAME' },
    //             ORDINAL_POSITION: { type: "number", path: '$.ORDINAL_POSITION' },
    //             IS_NULLABLE: { type: "string", path: '$.IS_NULLABLE' },
    //             DATA_TYPE: { type: "string", path: '$.DATA_TYPE' },
    //             FE: { type: "string", path: '$.FE' },
    //             Redis: { type: "string", path: '$.Redis' },
    //         }, {
    //             dataStructure: 'JSON',
    //         })
    //         var dataManager: { [p: string]: crudDA } = {
    //             '_data_manager': new crudDA(settingAllColSchema)
    //         }
    //         let _project = await dataManager['_data_manager'].groupBy({
    //             load: ['@TABLE_CATALOG'],
    //             steps: [
    //                 {
    //                     type: AggregateSteps.GROUPBY,
    //                     properties: ['@TABLE_CATALOG'],
    //                     REDUCE: [{
    //                         type: AggregateGroupByReducers.COUNT,
    //                         AS: 'count'
    //                     }]
    //                 }
    //             ]
    //         })
    //         for (let item of _project.results) {
    //             const result = await dataManager['_data_manager'].search(1, 100, `@TABLE_CATALOG:${item.TABLE_CATALOG}`)
    //             let schemaProject: any = {}
    //             for (let prop of result.data) {
    //                 schemaProject[prop.COLUMN_NAME] = { type: prop.Redis, path: `$.${prop.COLUMN_NAME}` }
    //             }
    //             dataManager[item.TABLE_CATALOG] = new crudDA(new Schema(`data:${item.TABLE_CATALOG}:column`, schemaProject, { dataStructure: 'JSON' }));
    //         }
    //         return dataManager
    //     default:
    //         const settingRepoColSchema = new Schema(`setting:${repo}:column`, {
    //             Id: { type: "string", path: '$.Id' },
    //             TABLE_CATALOG: { type: "text", path: '$.TABLE_CATALOG' },
    //             TABLE_NAME: { type: "text", path: '$.TABLE_NAME' },
    //             COLUMN_NAME: { type: "text", path: '$.COLUMN_NAME' },
    //             ORDINAL_POSITION: { type: "number", path: '$.ORDINAL_POSITION' },
    //             IS_NULLABLE: { type: "string", path: '$.IS_NULLABLE' },
    //             DATA_TYPE: { type: "string", path: '$.DATA_TYPE' },
    //             FE: { type: "string", path: '$.FE' },
    //             Redis: { type: "string", path: '$.Redis' },
    //         }, {
    //             dataStructure: 'JSON',
    //         })
    //         const columnRepo = new crudDA(settingRepoColSchema);
    //         let schemaManager: any = {}
    //         const allColumn = await columnRepo.getAll() as Array<any>
    //         for (let prop of allColumn) {
    //             schemaManager[prop.COLUMN_NAME] = { type: prop.Redis, path: `$.${prop.COLUMN_NAME}` }
    //         }
    //         var dataManager: { [p: string]: crudDA } = {
    //             '_data_manager': new crudDA(new Schema(`data:${repo}`, schemaManager, { dataStructure: 'JSON' }))
    //         }
    //         let _table = await columnRepo.groupBy({
    //             load: ['@TABLE_NAME'],
    //             steps: [
    //                 {
    //                     type: AggregateSteps.GROUPBY,
    //                     properties: ['@TABLE_NAME'],
    //                     REDUCE: [{
    //                         type: AggregateGroupByReducers.COUNT,
    //                         AS: 'count'
    //                     }]
    //                 }
    //             ]
    //         })
    //         for (let item of _table.results) {
    //             const result = await columnRepo.search(1, 100, `@TABLE_NAME:${item.TABLE_NAME}`)
    //             let schemaTable: any = {}
    //             for (let prop of result.data) {
    //                 schemaTable[prop.COLUMN_NAME] = { type: prop.Redis, path: `$.${prop.COLUMN_NAME}` }
    //             }
    //             dataManager[item.TABLE_NAME] = new crudDA(new Schema(`data:${repo}:${item.TABLE_NAME}`, schemaTable, { dataStructure: 'JSON' }));
    //         }
    //         return dataManager as any
    // }
}

export enum RepoPermission {
    root = 0,
    project = 1,
    table = 2
}
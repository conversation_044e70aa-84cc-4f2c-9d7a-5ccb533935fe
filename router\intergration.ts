import { Router } from "express";
import intergrationDA, { emailTemplates, transporter } from "../da/integrationDA";
import crudDA from "../da/crudDA";
import login from "../function/login";
import { get } from "../da/baseDA";
import { redis } from "../index";

const _Login = new login();

const router = Router();

const restartFirebaseAdmins = async () => {
  const configs = await redis.keys(`integration:firebaseconfig:*`);
  for (const _cfg of configs) {
    const intergration = new intergrationDA(_cfg.replace("integration:firebaseconfig:", ""));
    redis.json.get(_cfg).then((auth2key) => {
      intergration.initFirebaseAdmin(auth2key as any);
    });
  }
};
restartFirebaseAdmins();

router.post("/sendEmail", async (req, res) => {
  const { templateId, templateParams } = req.body;
  if (!templateParams || !templateId) return res.send({ code: 404, success: false, message: "missing templateId or templateParams" });
  const mailOptions = emailTemplates.find((e) => e.id === templateId);
  if (mailOptions) {
    if (Array.isArray(templateParams)) {
      templateParams.forEach((item, i) => {
        transporter.sendMail({ ...mailOptions.content(item), to: item.to }, function (error, info) {
          if (error) {
            console.log(error);
            if (i + 1 === templateParams.length) return res.send({ code: 500, success: false, message: "Email sending failed", error });
          } else {
            console.log("Email sent: " + info.response);
            if (i + 1 === templateParams.length) return res.send({ code: 200, success: true, message: "Email sent successfully", info });
          }
        });
      });
    } else {
      transporter.sendMail({ ...mailOptions.content(templateParams), to: templateParams.to }, function (error, info) {
        if (error) {
          console.log(error);
          return res.send({ code: 500, success: false, message: "Email sending failed", error });
        } else {
          console.log("Email sent: " + info.response);
          return res.send({ code: 200, success: true, message: "Email sent successfully", info });
        }
      });
    }
  } else return res.send({ code: 500, success: false, message: "Template not found" });
});

router.get("/geocode/json", async (req, res) => {
  const { latlng, key } = req.query;
  if (latlng && key) {
    const results = await get(`https://maps.googleapis.com/maps/api/geocode/json?latlng=${latlng}&key=${key}`);
    return res.status(200).json({ code: 200, message: "Success", data: results });
  } else {
    return res.send({ code: 404, message: "Missing latlng or key" });
  }
});

router.get("/place/textsearch/json", async (req, res) => {
  const { query, key } = req.query;
  if (query && key) {
    const results = await get(`https://maps.googleapis.com/maps/api/place/textsearch/json?&query=${query}&components=country:VN&key=${key}`);
    return res.status(200).json({ code: 200, message: "Success", data: results });
  } else {
    return res.send({ code: 404, message: "Missing query or key" });
  }
});

router.post("/action", async (req, res) => {
  const { pid } = req.headers;
  const { name, headers, body, query, params, menthod, url, mapSetting, addField, data, descriptions } = req.body;
  const { action } = req.query;

  const intergration = new intergrationDA(pid as string);
  const result = await intergration.action(action as string, { name, headers, body, query, params, menthod, url, mapSetting, addField, data, descriptions });
  return res.status(result.code).json({ code: result.code, data: result.data, message: result.message ?? "" });
});

router.post("/initFirebaseAdmin", async (req, res) => {
  const { pid } = req.headers;
  if (!pid) return res.send({ code: 404, message: "Missing pid" });
  const { auth2key } = req.body;

  const intergration = new intergrationDA(pid as string);
  await intergration.initFirebaseAdmin(auth2key as any);
  return res.status(200).json({ code: 200, message: "success" });
});

router.get("/getFirebaseAdmin", async (req, res) => {
  const { pid } = req.headers;
  if (!pid) return res.send({ code: 404, message: "Missing pid" });

  const intergration = new intergrationDA(pid as string);
  const result = await intergration.getFirebaseAdmin();
  return res.status(200).json({ code: 200, data: result, message: "success" });
});

router.post("/intergration", async (req, res) => {
  const { pid } = req.headers;
  if (!pid) return res.send({ code: 404, message: "Missing pid" });
  const { keys } = req.body;

  const intergration = new intergrationDA(pid as string);
  const result = await intergration.integration(keys as Array<string>);
  return res.json({ message: "ok" });
});

router.post("/ebig/login", async (req, res) => {
  const { pid, module } = req.headers;
  const _moduleRepo = new crudDA(`data:${pid}:${module}`);
  const { UserName, PassWord } = req.body;
  const userLogin = (await _moduleRepo.search(1, 10, `@UserName:(${UserName})`, { RETURN: ["Id", "UserName", "PassWord"] })) as any;
  if (!userLogin.count) {
    return res.send({ code: 404, message: "User not found" });
  } else {
    const isMatch = await _Login.verifyPassword(PassWord, userLogin.data?.[0].PassWord);
    if (!isMatch) {
      return res.send({ code: 403, message: "Invalid password" });
    } else {
      const payload = { id: userLogin.data[0].Id, username: userLogin.data[0].UserName };
      const accessToken = _Login.createAccessToken(payload);
      const refreshToken = _Login.createRefreshToken(payload);
      return res.status(200).json({ data: { accessToken, refreshToken }, code: 200, message: "Success" });
    }
  }
});

export default router;

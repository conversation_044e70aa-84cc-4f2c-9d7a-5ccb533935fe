import axios from 'axios';

const header = {
    "Content-Type": "application/json"
}

export async function get(url: string) {
    try {
        const response = await axios.get(url, { headers: header })
        if (response.status === 200) {
            return response.data
        } else {
            console.log("error: ??: ", response.statusText)
            return { status: response.status, message: response.statusText };
        }
    } catch (error) {
        console.log("error: ??: ", JSON.stringify(error))
        return { status: 500, message: 'error' };
    }
};

export async function post(url: string, body: any) {
    try {
        const response = await axios.post(url, body, { headers: header })
        if (response.status === 200) {
            return response.data
        } else {
            console.log("error: ??: ", response.statusText)
            return { status: response.status, message: response.statusText };
        }
    } catch (error) {
        console.log("error: ??: ", JSON.stringify(error))
        return { status: 500, message: 'error' };
    }
};

export const ConfigDomain = ["localhost:3001", "admin.wini.vn"]
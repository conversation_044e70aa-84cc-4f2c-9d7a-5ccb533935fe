import login from "../function/login";
import jwt from "jsonwebtoken";
import { get, post } from "./baseDA";
import { toSeconds, toSecondsDateString } from "../Ultis/convert";
import { randomUUID } from "crypto";
import crudDA from "../da/crudDA";
import admin from "firebase-admin";
import { redis } from "../index";
import nodemailer from "nodemailer";

const _Login = new login();
const winiAdminEmail = "<EMAIL>";
const winiAppPassword = "qrac lfkn yyul bmrj";

export const transporter = nodemailer.createTransport({
  service: "gmail",
  auth: {
    user: winiAdminEmail,
    pass: winiAppPassword,
  },
});

interface Intergration {
  name: string;
  headers: string;
  body: string;
  query: string;
  params: string;
  menthod: string;
  url: string;
  mapSetting: Array<any>;
  addField: Array<any>;
  data: string;
  descriptions: string;
}

// <PERSON><PERSON>nh ngh<PERSON>a ki<PERSON>u dữ liệu cho request
interface FCMRequest {
  noti: {
    title: string;
    body: string;
    imageUrl?: string; // Không bắt buộc
  };
  data: {
    id: string | number;
    type: string | number;
    url: string;
  };
  deviceToken?: string; // Chỉ dùng cho `sendMessageToDevice`
  deviceTokens?: string[]; // Chỉ dùng cho `sendMessageToGroup`
}

const firebaseApps: { [key: string]: admin.app.App } = {};
export default class intergrationDA {
  private pid: string;
  private projectAdmin: admin.app.App;
  constructor(id: string) {
    this.projectAdmin = firebaseApps[id];
    this.pid = id;
  }

  async initFirebaseAdmin(serviceAccount: admin.ServiceAccount) {
    if (!serviceAccount) {
      throw new Error(`Service account is required for initializing Firebase project: ${this.pid}`);
    }
    if (firebaseApps[this.pid]) await firebaseApps[this.pid].delete();
    firebaseApps[this.pid] = admin.initializeApp(
      {
        credential: admin.credential.cert(serviceAccount),
      },
      this.pid // Use the project ID as the app name
    );
    await redis.json.set(`integration:firebaseconfig:${this.pid}`, "$", serviceAccount as any);
    return firebaseApps[this.pid];
  }

  async getFirebaseAdmin() {
    const result = await redis.json.get(`integration:firebaseconfig:${this.pid}`);
    return result;
  }

  async removeFirebaseAdmin() {
    delete firebaseApps[this.pid];
    await redis.json.del(`integration:firebaseconfig:${this.pid}`);
  }

  async login(params: { type: string; token: string; deviceToken: string; ggClientId?: string; clientSecret?: string }) {
    switch (params.type) {
      case "apple":
        try {
          const json = jwt.decode(params.token, { complete: true }) as jwt.Jwt | null;
          if (!json || !json.header.kid) {
            return { code: 400, message: "Invalid token" };
          }
          const kid: string = json.header.kid;
          const appleKey = await _Login.getAppleSigningKey(kid);
          const payload = await _Login.verifyJWT(params.token, appleKey);
          if (payload) {
            return { code: 200, data: payload };
          } else {
            return { code: 404, message: "Login Apple failed" };
          }
        } catch (error) {
          return { code: 404, message: `${error}` };
        }
      case "google":
        if (params.ggClientId && params.clientSecret) {
          try {
            const payload = await _Login.verifyGoogleLogin(params.ggClientId, params.token, params.clientSecret);
            if (payload && payload.code !== 404) {
              return { code: 200, data: payload };
            } else {
              return payload as any;
            }
          } catch (error) {
            return { code: 404, message: `${error}` };
          }
        } else {
          return { code: 404, message: "Missing google clientID" };
        }
      case "microsoft":
        try {
          // Xác thực token với Firebase
          const payload = await this.projectAdmin.auth().verifyIdToken(params.token);
          console.log("Decoded Firebase Token:", payload);

          // // Nếu cần lấy thêm thông tin từ Microsoft Graph API
          // const accessToken = decodedToken.firebase.identities["microsoft.com"][0];
          // console.log("Microsoft Access Token:", accessToken);

          // Giải mã hoặc xác thực Access Token Microsoft nếu cần
          // const microsoftClaims = jwt.decode(accessToken);
          // console.log("Microsoft Claims:", microsoftClaims);
          // if (decodedToken && microsoftClaims) {
          return { code: 200, data: payload };
        } catch (error) {
          return { code: 404, message: `${error}` };
        }
      default:
        return { code: 404, message: "Invalid login type" };
    }
  }

  async mapApi(menthod: string, url: string, body?: string) {
    const res = menthod.toLowerCase() == "get" ? await get(`${url}`) : await post(`${url}`, body);
    return res;
  }

  async action(action: string, dataItg: Intergration) {
    const { name, headers, body, query, params, menthod, url, mapSetting, addField, data, descriptions } = dataItg;
    const _moduleRepo = new crudDA(`integration:${this.pid}:setting`);
    const id = randomUUID();
    let _data = [
      {
        id,
        name,
        headers,
        body,
        query,
        params,
        menthod,
        url,
        mapSetting, // Array<{key: string, value: string, convert?: boolean}>
        addField,
        data,
        descriptions,
        dateCreated: toSeconds(Date.now()),
      },
    ];
    switch (`${action}`.toLowerCase()) {
      case "add":
        try {
          await _moduleRepo.action("add", _data);
          return { data: data, code: 200, message: "Success" };
        } catch (error) {
          return { code: 404, message: (error as any).message ?? "" };
        }
      case "edit":
        try {
          await _moduleRepo.action("edit", _data);
          return { data: data, code: 200, message: "Success" };
        } catch (error) {
          return { code: 404, message: (error as any).message ?? "" };
        }
      default:
        return { code: 404, message: "Invalid action" };
    }
  }

  async integration(keys: Array<string>) {
    const _moduleRepo = new crudDA(`integration:${this.pid}:data`);
    // const _moduleRepo = new crudDA(`data:${pid}:${module}`)

    // setting data
    keys.map(async (key) => {
      const setting: any = await _moduleRepo.getKey(key);

      // convert date
      const dataResponse = await this.mapApi(setting.menthod, setting.url, setting.body);

      //convert - mapSetting
      const data: Array<any> = [];

      dataResponse.data.map((_d: any) => {
        const temp: any = {};
        const field: any = {};
        setting.mapSetting.map((_s: any) => {
          _s.convert ? (temp[`${_s.key.match(/\$\.(.*)/)[1]}`] = toSecondsDateString(_d[`${_s.value}`])) : (temp[`${_s.key.match(/\$\.(.*)/)[1]}`] = _d[`${_s.value}`]);
        });
        setting.addField.map((_f: any) => {
          temp[_f.key] = _f.value;
        });
        data.push({ ...temp });
      });
      try {
        const results = await _moduleRepo.action("add", data);
        return { data: results, code: 200, message: "Success" };
      } catch (err) {
        return { code: 404, message: (err as any).message ?? "" };
      }
    });
  }

  // Gửi thông báo đến tất cả các thiết bị (theo topic)
  async sendMessageAll(request: FCMRequest) {
    const message = {
      notification: {
        title: request.noti.title,
        body: request.noti.body,
        imageUrl: request.noti.imageUrl, // Tùy chọn
      },
      data: {
        Id: request.data.id.toString(),
        Type: request.data.type.toString(),
      },
      topic: "all-devices", // Topic mặc định
    };

    try {
      const response = await this.projectAdmin.messaging().send(message);
      console.log("Successfully sent message to all devices:", response);
      return { data: response, code: 200, message: "Success" };
    } catch (error) {
      console.error("Error sending message to all devices:", error);
      return { code: 404, message: (error as any).message ?? "" };
    }
  }

  // Gửi thông báo đến một thiết bị cụ thể
  async sendMessageToDevice(request: FCMRequest) {
    if (!request.deviceToken) {
      throw new Error("Device token is required for sendMessageToDevice");
    }

    const message = {
      notification: {
        title: request.noti.title,
        body: request.noti.body,
      },
      data: {
        Id: request.data.id.toString(),
        Type: request.data.type.toString(),
      },
      token: request.deviceToken, // Device token
    };

    try {
      const response = await this.projectAdmin.messaging().send(message);
      console.log("Successfully sent message to device:", response);
      return { data: response, code: 200, message: "Success" };
    } catch (error) {
      console.error("Error sending message to device:", error);
      return { code: 404, message: (error as any).message ?? "" };
    }
  }

  // Gửi thông báo đến một nhóm thiết bị (multicast)
  async sendMessageToGroup(request: FCMRequest) {
    if (!request.deviceTokens || request.deviceTokens.length === 0) {
      throw new Error("Device tokens are required for sendMessageToGroup");
    }

    const message = {
      notification: {
        title: request.noti.title,
        body: request.noti.body,
      },
      data: {
        Id: request.data.id.toString(),
        Type: request.data.type.toString(),
      },
      tokens: request.deviceTokens, // Danh sách token
    };

    try {
      const response = await this.projectAdmin.messaging().sendEachForMulticast(message);
      console.log("Successfully sent messages:", response.successCount);
      console.log("Failures:", response.failureCount);
      if (response.failureCount > 0) {
        console.error(
          "Failed tokens:",
          response.responses.filter((r) => !r.success).map((r) => r.error)
        );
      }
      return { data: response, code: 200, message: "Success" };
    } catch (error) {
      console.error("Error sending messages to group:", error);
      return { code: 404, message: (error as any).message ?? "" };
    }
  }

  // Gửi thông báo đến một nhóm thiết bị (multicast)
  async otp(idToken: string) {
    const decodedToken = await this.projectAdmin.auth().verifyIdToken(idToken);
    const uid = decodedToken.uid; // Lấy UID của người dùng
    return uid;
  }
}

export enum NotiType {
  invite = 1,
  acceptInvite = 2,
  rejectInvite = 3,
  assignTask = 4,
  changeTaskStatus = 5,
  newOrder = 6,
  acceptOrder = 7,
  changeToiletStatus = 8,
  changeToiletServiceStatus = 9,
  registerPartner = 10,
  registerGreen = 11,
  changeContractStatus = 12,
  changeAddendumStatus = 13,
  changeTicketStatus = 14,
  post = 15,
  news = 16,
  comment = 17,
  mention = 18,
  like = 19,
  groupRole = 20,
  request = 21,
  acceptRequest = 22,
  rejectRequest = 23,
}

export const emailTemplates = [
  {
    id: "26912ed147ab449db1aca143cdca2d02", // invite to project
    content: (params: { fromName: string; projectName: string; inviteId: string }) => {
      return {
        from: winiAdminEmail,
        subject: `${params.fromName} invited you to join ${params.projectName}`,
        html: `
            <div style="font-family: system-ui, sans-serif, Arial; font-size: 16px; background-color: #fff8f1;">
                <div style="max-width: 600px; margin: auto; padding: 16px;">
                    <a style="text-decoration: none; outline: none;" href="https://admin.wini.vn" target="_blank" rel="noopener">
                        <img style="height: 32px; vertical-align: middle" src="cid:winilogo" alt="logo" height="32px">
                    </a>
                    <p>
                        ${params.fromName} has invited you to <span style="font-weight: 600; font-size: 18px">"${params.projectName}"</span>
                        <p>
                            <a style="display: inline-block;text-decoration: none; outline: none; color: #fff; background-color: #287CF0; padding: 8px 16px; border-radius: 4px;" href="https://admin.wini.vn/accept?invite=${params.inviteId}" target="_blank" rel="noopener">Accept</a>
                        </p>
                        <p>If you have any questions or need help getting started, our support team is just an email away at <a style="text-decoration: none; outline: none; color: #287CF0;" href="mailto:${winiAdminEmail}">Wini technology</a>. We're here to assist you every step of the way!
                    </p>
                    <p>Best regards,<br>The Wini Team</p>
                </div>
            </div>`,
        attachments: [
          {
            filename: "LogoWini.png",
            path: "./assets/LogoWini.png",
            cid: "winilogo", // Content-ID (matches the `src="cid:winilogo"`)
          },
        ],
      };
    },
  },
];

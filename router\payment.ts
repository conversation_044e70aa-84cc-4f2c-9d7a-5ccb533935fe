// Cài đặt các package cần thiết:
// npm install express crypto dotenv querystring moment cors
import { Router } from "express";
import  crypto from 'crypto';
import  querystring from 'querystring';
import  moment from 'moment';
import dotenv from 'dotenv';
import os from 'os';
import crudDA from "../da/crudDA";
dotenv.config();
const router = Router();

/**
 * @swagger
 * components:
 *   schemas:
 *     PaymentUrlRequest:
 *       type: object
 *       required:
 *         - Id
 *         - Price
 *         - Description
 *         - orderType
 *       properties:
 *         Id:
 *           type: string
 *           description: Mã đơn hàng
 *         Price:
 *           type: number
 *           description: Số tiền thanh toán (VND)
 *         Description:
 *           type: string
 *           description: Mô tả đơn hàng
 *         orderType:
 *           type: string
 *           description: Loại đơn hàng
 *         language:
 *           type: string
 *           enum: [vn, en]
 *           default: vn
 *           description: Ngôn ngữ hiển thị
 *     PaymentUrlResponse:
 *       type: object
 *       properties:
 *         code:
 *           type: string
 *           example: "00"
 *         message:
 *           type: string
 *           example: "success"
 *         data:
 *           type: string
 *           description: URL thanh toán VNPay
 *     PaymentReturnResponse:
 *       type: object
 *       properties:
 *         code:
 *           type: string
 *           example: "00"
 *         message:
 *           type: string
 *           example: "Giao dịch thành công"
 *         data:
 *           type: object
 *           description: Thông tin giao dịch từ VNPay
 *     PaymentIPNResponse:
 *       type: object
 *       properties:
 *         RspCode:
 *           type: string
 *           example: "00"
 *         Message:
 *           type: string
 *           example: "success"
 */

// Cấu hình VNPay từ biến môi trường
const VNP_TMN_CODE = process.env.VNP_TMN_CODE; // Terminal ID được VNPay cấp
const VNP_HASH_SECRET = process.env.VNP_HASH_SECRET; // Secret key được VNPay cấp
const VNP_URL = process.env.VNP_URL; // URL sandbox hoặc production
const VNP_RETURN_URL = process.env.VNP_RETURN_URL;
const VNP_RETURN_URL_Mobile = process.env.VNP_RETURN_URL_Mobile;

/**
 * @swagger
 * /vnpay/create_payment_url:
 *   post:
 *     summary: Tạo URL thanh toán VNPay
 *     description: Tạo URL thanh toán để chuyển hướng người dùng đến cổng thanh toán VNPay
 *     tags: [Payment]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/PaymentUrlRequest'
 *     responses:
 *       200:
 *         description: URL thanh toán được tạo thành công
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/PaymentUrlResponse'
 *       500:
 *         description: Lỗi server
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: string
 *                   example: "99"
 *                 message:
 *                   type: string
 *                   example: "Error creating payment URL"
 *                 data:
 *                   type: null
 */
// Tạo endpoint để khởi tạo giao dịch thanh toán
router.post("/create_payment_url", async (req, res) => {
  try {
    const { Id, Price, Description, orderType, language = "vn", os } = req.body;

    // Mã đơn hàng, sử dụng timestamp để tạo mã đơn hàng duy nhất

    // Thời gian tạo giao dịch
    const createDate = moment().format("YYYYMMDDHHmmss");

    // Tạo đối tượng chứa dữ liệu thanh toán
    const vnpParams = {
      vnp_Version: "2.1.0",
      vnp_Command: "pay",
      vnp_TmnCode: VNP_TMN_CODE,
      vnp_Locale: language,
      vnp_CurrCode: "VND",
      vnp_TxnRef: Id,
      vnp_OrderInfo: Description,
      vnp_OrderType: orderType,
      vnp_Amount: Price * 100, // Số tiền * 100 (VNPay yêu cầu)
      vnp_ReturnUrl: os === "mobile" ? VNP_RETURN_URL_Mobile : VNP_RETURN_URL,
      vnp_IpAddr: req.ip || "127.0.0.1",
      vnp_CreateDate: createDate,
    };

    // Sắp xếp các tham số theo thứ tự alphabet
    const sortedParams = sortObject(vnpParams);

    // Tạo chuỗi ký
    const signData = querystring.stringify(sortedParams);

    // Tạo chữ ký
    const hmac = crypto.createHmac("sha512", VNP_HASH_SECRET ?? "");
    const signed = hmac.update(Buffer.from(signData, "utf-8")).digest("hex");

    // Thêm chữ ký vào object params
    sortedParams.vnp_SecureHash = signed;

    // Tạo URL thanh toán
    const paymentUrl = `${VNP_URL}?${querystring.stringify(sortedParams)}`;

    // Trả về URL thanh toán
    return res.status(200).json({
      code: "00",
      message: "success",
      data: paymentUrl,
    });
  } catch (error) {
    console.error("Error creating payment URL:", error);
    return res.status(500).json({
      code: "99",
      message: "Error creating payment URL",
      data: null,
    });
  }
});

/**
 * @swagger
 * /vnpay/vnpay_return:
 *   get:
 *     summary: Xử lý callback từ VNPay
 *     description: Xử lý kết quả thanh toán khi người dùng được chuyển hướng từ cổng thanh toán VNPay về
 *     tags: [Payment]
 *     parameters:
 *       - in: query
 *         name: vnp_Amount
 *         schema:
 *           type: string
 *         description: Số tiền thanh toán
 *       - in: query
 *         name: vnp_ResponseCode
 *         schema:
 *           type: string
 *         description: Mã phản hồi từ VNPay
 *       - in: query
 *         name: vnp_TxnRef
 *         schema:
 *           type: string
 *         description: Mã đơn hàng
 *       - in: query
 *         name: vnp_SecureHash
 *         schema:
 *           type: string
 *         description: Chữ ký xác thực
 *     responses:
 *       200:
 *         description: Xử lý callback thành công
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/PaymentReturnResponse'
 *       400:
 *         description: Chữ ký không hợp lệ
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: string
 *                   example: "97"
 *                 message:
 *                   type: string
 *                   example: "Chữ ký không hợp lệ"
 *                 data:
 *                   type: null
 */
// API để xử lý callback từ VNPay
router.get("/vnpay_return", (req, res) => {
  try {
    const vnpParams = req.query;
    const secureHash = vnpParams["vnp_SecureHash"];

    // Xóa chữ ký từ object để tính toán lại
    delete vnpParams["vnp_SecureHash"];
    delete vnpParams["vnp_SecureHashType"];

    // Sắp xếp các tham số
    const sortedParams = sortObject(vnpParams);

    // Tạo chuỗi để kiểm tra chữ ký
    const signData = querystring.stringify(sortedParams);

    // Tạo chữ ký để kiểm tra
    const hmac = crypto.createHmac("sha512", VNP_HASH_SECRET ?? "");
    const signed = hmac.update(Buffer.from(signData, "utf-8")).digest("hex");

    // Kiểm tra chữ ký
    if (secureHash === signed) {
      // Xác nhận giao dịch thành công hay thất bại dựa trên mã giao dịch
      const responseCode = vnpParams["vnp_ResponseCode"];
      //có thể thực hiện lưu tạm thông tin của giao dịch vào database

      // Trả về kết quả xử lý cho người dùng
      if (responseCode === "00") {
        return res.status(200).json({
          code: "00",
          message: "Giao dịch thành công",
          data: vnpParams,
        });
      } else {
        return res.status(200).json({
          code: responseCode,
          message: "Giao dịch thất bại",
          data: vnpParams,
        });
      }
    } else {
      return res.status(400).json({
        code: "97",
        message: "Chữ ký không hợp lệ",
        data: null,
      });
    }
  } catch (error) {
    console.error("Error handling VNPay return:", error);
    return res.status(500).json({
      code: "99",
      message: "Error handling VNPay return",
      data: null,
    });
  }
});

/**
 * @swagger
 * /vnpay/vnpay_ipn:
 *   post:
 *     summary: Xử lý IPN (Instant Payment Notification) từ VNPay
 *     description: Xử lý thông báo thanh toán tức thì từ máy chủ VNPay
 *     tags: [Payment]
 *     parameters:
 *       - in: query
 *         name: vnp_Amount
 *         schema:
 *           type: string
 *         description: Số tiền thanh toán
 *       - in: query
 *         name: vnp_ResponseCode
 *         schema:
 *           type: string
 *         description: Mã phản hồi từ VNPay
 *       - in: query
 *         name: vnp_TxnRef
 *         schema:
 *           type: string
 *         description: Mã đơn hàng
 *       - in: query
 *         name: vnp_TransactionNo
 *         schema:
 *           type: string
 *         description: Mã giao dịch VNPay
 *       - in: query
 *         name: vnp_SecureHash
 *         schema:
 *           type: string
 *         description: Chữ ký xác thực
 *     responses:
 *       200:
 *         description: Xử lý IPN thành công
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/PaymentIPNResponse'
 */
// API để xử lý IPN (Instant Payment Notification) từ VNPay
router.post("/vnpay_ipn", async (req, res) => {
  try {
    const vnpParams = req.query;
    const secureHash = vnpParams["vnp_SecureHash"];

    // Xóa chữ ký từ object để tính toán lại
    delete vnpParams["vnp_SecureHash"];
    delete vnpParams["vnp_SecureHashType"];

    // Sắp xếp các tham số
    const sortedParams = sortObject(vnpParams);

    // Tạo chuỗi để kiểm tra chữ ký
    const signData = querystring.stringify(sortedParams);

    // Tạo chữ ký để kiểm tra
    const hmac = crypto.createHmac("sha512", VNP_HASH_SECRET ?? "");
    const signed = hmac.update(Buffer.from(signData, "utf-8")).digest("hex");

    // Kiểm tra chữ ký
    if (secureHash === signed) {
      // Xác nhận giao dịch thành công hay thất bại dựa trên mã giao dịch
      const responseCode = vnpParams["vnp_ResponseCode"];
      const transactionId = vnpParams["vnp_TransactionNo"] as string;
      const orderId = vnpParams["vnp_TxnRef"] as string;
      if (responseCode === "00") {
        // Cập nhật trạng thái đơn hàng vào OrderDetail thành công trong database
        //Ngoài việc update trạng thái cho đơn hàng. lưu thêm thông tin transactionId và vnpParams để sử dụng lại sau này nếu cần
        // updateOrderStatus(orderId, transactionId, Success, vnpParams);
        await updateOrderStatus(orderId, transactionId, 3, vnpParams, vnpParams["vnp_OrderInfo"] as string);
      } else {
        // Cập nhật trạng thái đơn hàng vào OrderDetail thất bại trong database
        //Ngoài việc update trạng thái cho đơn hàng. lưu thêm thông tin transactionId và vnpParams để sử dụng lại sau này nếu cần
        // updateOrderStatus(orderId, transactionId, Fail, vnpParams);
        await updateOrderStatus(orderId, transactionId, 4, vnpParams, vnpParams["vnp_OrderInfo"] as string);
      }
      // Trả về kết quả cho VNPay
      res.status(200).json({ RspCode: "00", Message: "success" });
    } else {
      res.status(200).json({ RspCode: "97", Message: "Invalid Signature" });
    }
  } catch (error) {
    console.error("Error handling VNPay IPN:", error);
    res.status(200).json({ RspCode: "99", Message: "Unknown error" });
  }
});


function sortObject(obj: any) {
  const sorted = <any>{};
  const keys = Object.keys(obj).sort();

  for (const key of keys) {
    if (obj.hasOwnProperty(key)) {
      sorted[key] = obj[key];
    }
  }

  return sorted;
}
async function updateOrderStatus(Id: string, transactionId: string, status: number, vnpParams: any, courseCustomer: string) {
  const pid = process.env.PID ?? "";
  const module = "Order";
  const _moduleRepo = new crudDA(`data:${pid}:${module}`);
  const order = (await _moduleRepo.getById(Id)) as any;
  order.Status = status;
  order.Transaction = transactionId;
  order.VnpParams = vnpParams;
  await _moduleRepo.action("edit", [order]);
  if (courseCustomer) {
    const _moduleRepo2 = new crudDA(`data:${pid}:Course_Customer`);
    const courseLesson = (await _moduleRepo2.getById(courseCustomer)) as any;
    courseLesson.Status = status;
   await _moduleRepo2.action("edit", [courseLesson]);
  }
}

export default router;
function getPlatformOS(userAgent?: string): string {
  if (!userAgent) {
    // Return server OS if no user agent provided
    return os.platform();
  }
  
  // Check for iOS devices
  if (/iPhone|iPad|iPod/i.test(userAgent)) {
    return 'ios';
  }
  
  // Check for Android devices
  if (/Android/i.test(userAgent)) {
    return 'android';
  }
  
  // If not mobile, assume it's web
  return 'web';
}
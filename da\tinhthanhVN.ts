import { get, post } from './baseDA';
import { randomGID, toSeconds, toSecondsDateString } from '../Ultis/convert';
import crudDA from '../da/crudDA';
import { stringify } from 'querystring';

export default class tinhThanhVN {
  constructor() {}

  async mapApi(menthod: string, url: string, body?: string) {
    const res = menthod.toLowerCase() == 'get' ? await get(`${url}`) : await post(`${url}`, body);
    return res;
  }

  async action(action: string) {
    const _moduleRepo = new crudDA(`tinhthanh:setting`);
    const listProvinces = await this.mapApi("GET", "https://esgoo.net/api-tinhthanh/1/0.htm", "");
    const temp = {
      Name: "API lấy thông tin Tỉnh Th<PERSON>nh, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ng Xã Việt Nam",
      Headers:"",
      Body:"", 
      Query:"", 
      Params:"", 
      Menthod:"GET",
      Url:"https://esgoo.net/api-tinhthanh/1/0.htm", 
      MapSetting:[
          {
              key:"$.Id",
              value:"id",
              convert: false
          },
          {
              key:"$.Name",
              value:"name",
              convert: false
          },
          {
              key:"$.NameEn",
              value:"name_en",
              convert: false
          },
          {
              key:"$.FullName",
              value:"full_name",
              convert: false
          },
          {
              key:"$.FullNameEn",
              value:"full_name_en",
              convert: false
          },
          {
              key:"$.Latitude",
              value:"latitude",
              convert: false
          },
          {
              key:"$.Longitude",
              value:"longitude",
              convert: false
          }
      ],
      Description:"Api lấy tất cả tỉnh thành (63 tỉnh thành)",
      Type: 1,
      Data:""
    };
    const _settingProvinces: any = [{
      Id: randomGID(),
      ...temp
    }];
    await _moduleRepo.action('add', _settingProvinces);

    listProvinces.data.map(async (provinces: any) => {
    const id = randomGID();
    let _settingDistrict = [
      {
        Id: id,
        ...temp,
        Url: `https://esgoo.net/api-tinhthanh/2/${provinces.id}.htm`,
        AddField:[
          {
              key:"ProvinceId",
              value: provinces.id
          }
        ],
        Descriptions:'Api lấy quận huyện từ id tỉnh thành',
        Type: 2,
        DateCreated: toSeconds(Date.now())
      }
    ];

    switch (`${action}`.toLowerCase()) {
      case 'add':
        try {
          await _moduleRepo.action('add', _settingDistrict);
        } catch (error) {
          return { code: 404, message: (error as any).message ?? '' };
        }
        break;
      case 'edit':
        try {
          await _moduleRepo.action('edit', _settingDistrict);
        } catch (error) {
          return { code: 404, message: (error as any).message ?? '' };
        }
        break;
      default:
        return { code: 404, message: 'Invalid action' };
    }
    const listDistricts = await this.mapApi("GET", `https://esgoo.net/api-tinhthanh/2/${provinces.id}.htm`, "");

    listDistricts.data.map(async (districts: any) => {
      const id = randomGID();
      let _settingWard = [
        {
          Id: id,
          ...temp,
          Url: `https://esgoo.net/api-tinhthanh/3/${districts.id}.htm`,
          AddField:[
            {
                key:"DistrictId",
                value: districts.id
            }
          ],
          Descriptions:'Api lấy phường xã từ id quận huyện',
          Type: 3,
          DateCreated: toSeconds(Date.now())
        }
      ];

      switch (`${action}`.toLowerCase()) {
        case 'add':
          try {
            await _moduleRepo.action('add', _settingWard)
          } catch (error) {
            return { code: 404, message: (error as any).message ?? '' }
          }
          break;
        case 'edit':
          try {
            await _moduleRepo.action('edit', _settingWard)
          } catch (error) {
            return { code: 404, message: (error as any).message ?? '' }
          }
          break;
        default:
          return { code: 404, message: 'Invalid action' }
        }
      })
    })
  };

  // async integration() {
  //   const _dataRepo = new crudDA(`tinhthanh:data`)
  //   const _settingRepo = new crudDA(`tinhthanh:setting`)

  //   const listKeys = await _settingRepo.getAll()
  //   // setting data
  //   const data: Array<any> = [];
  //   await Promise.all(
  //     listKeys.filter((key: any) => key.Type === 1).map(async (key: any) => {
  //       const provinces = await this.mapApi(key.Menthod, key.Url, key.Body);

  //       provinces.data.map(async (_p: any) => {
  //         const provinceTemp: any = {};
  //         key.MapSetting.map((_s: any) => {
  //           _s.convert ?
  //           provinceTemp[`${_s.key.match(/\$\.(.*)/)[1]}`] = toSecondsDateString(_p[`${_s.value}`])
  //             :
  //             provinceTemp[`${_s.key.match(/\$\.(.*)/)[1]}`] = _p[`${_s.value}`]
  //         });
  //         provinceTemp["GID"] = randomGID();
  //         data.push({ ...provinceTemp });
  //         await Promise.all(
  //           listKeys.filter((key: any) => key.Type === 2 && key.Url === `https://esgoo.net/api-tinhthanh/2/${_p.id}.htm`).map(async (key: any) => {
  //             const districts = await this.mapApi(key.Menthod, key.Url, key.Body);

  //             districts.data.map(async (_d:any) => {
  //               const districtTemp: any = {};
  //               key.MapSetting.map((_s: any) => {
  //                 _s.convert ?
  //                   districtTemp[`${_s.key.match(/\$\.(.*)/)[1]}`] = toSecondsDateString(_d[`${_s.value}`])
  //                   :
  //                   districtTemp[`${_s.key.match(/\$\.(.*)/)[1]}`] = _d[`${_s.value}`]
  //               });
  //               key.AddField.map((_f: any) => {
  //                 const gid = data.find((e: any) => e.Id === _f.value).Id
  //                 districtTemp[_f.key] = gid;
  //               })
  //               districtTemp["GID"] = randomGID();
  //               data.push({ ...districtTemp });
  //               await Promise.all(
  //               listKeys.filter((key: any) => key.Type === 3 && key.Url === `https://esgoo.net/api-tinhthanh/3/${_d.id}.htm`).map(async (key: any) => {
  //                 const wards = await this.mapApi(key.Menthod, key.Url, key.Body);
                  
  //                   wards.data.map((_w:any) => {
  //                     const wardTemp: any = {};
  //                     key.MapSetting.map((_s: any) => {
  //                       _s.convert ?
  //                         wardTemp[`${_s.key.match(/\$\.(.*)/)[1]}`] = toSecondsDateString(_w[`${_s.value}`])
  //                         :
  //                         wardTemp[`${_s.key.match(/\$\.(.*)/)[1]}`] = _w[`${_s.value}`]
  //                     });
  //                     key.AddField.map((_f: any) => {
  //                       const gid = data.find((e: any) => e.Id === _f.value).GID;
  //                       wardTemp[_f.key] = gid;
  //                     })
  //                     wardTemp["GID"] = randomGID();
  //                     data.push({ ...wardTemp });
  //                   })
  //               })
  //               );
  //             })
  //           })
  //         );
  //       });
  //     })
  //   );
  //   try {
  //     console.log(data.length)
  //     // const results = await _dataRepo.action('add', data);
  //     // return { data: results, code: 200, message: 'Success' }
  //   }
  //   catch (err) {
  //     return { code: 404, message: (err as any).message ?? '' }
  //   }
  //     // const a = data;
  //     // console.log(data)
  // };

  async integration() {
    const _dataRepo = new crudDA(`tinhthanh:data`);
    const _settingRepo = new crudDA(`tinhthanh:setting`);
  
    const listKeys = await _settingRepo.getAll();
    const data: Array<any> = [];
  
    // Xử lý các provinces
    await Promise.all(
      listKeys
        .filter((key: any) => key.Type === 1)
        .map(async (key: any) => {
          const provinces = await this.mapApi(key.Menthod, key.Url, key.Body);
  
          await Promise.all(
            provinces.data.map(async (_p: any) => {
              const provinceTemp: any = {};
              key.MapSetting.forEach((_s: any) => {
                _s.convert
                  ? (provinceTemp[`${_s.key.match(/\$\.(.*)/)[1]}`] = toSecondsDateString(_p[`${_s.value}`]))
                  : (provinceTemp[`${_s.key.match(/\$\.(.*)/)[1]}`] = _p[`${_s.value}`]);
              });
              provinceTemp["GID"] = randomGID();
              data.push({ ...provinceTemp });
  
              // Xử lý các districts
              await Promise.all(
                listKeys
                  .filter(
                    (districtKey: any) =>
                      districtKey.Type === 2 &&
                      districtKey.Url === `https://esgoo.net/api-tinhthanh/2/${_p.id}.htm`
                  )
                  .map(async (districtKey: any) => {
                    const districts = await this.mapApi(districtKey.Menthod, districtKey.Url, districtKey.Body);
  
                    await Promise.all(
                      districts.data.map(async (_d: any) => {
                        const districtTemp: any = {};
                        districtKey.MapSetting.forEach((_s: any) => {
                          _s.convert
                            ? (districtTemp[`${_s.key.match(/\$\.(.*)/)[1]}`] = toSecondsDateString(_d[`${_s.value}`]))
                            : (districtTemp[`${_s.key.match(/\$\.(.*)/)[1]}`] = _d[`${_s.value}`]);
                        });
  
                        districtKey.AddField.forEach((_f: any) => {
                          const gid = data.find((e: any) => e.Id === _f.value)?.GID;
                          districtTemp[_f.key] = gid;
                        });
                        districtTemp["GID"] = randomGID();
                        data.push({ ...districtTemp });
  
                        // Xử lý các wards
                        await Promise.all(
                          listKeys
                            .filter(
                              (wardKey: any) =>
                                wardKey.Type === 3 &&
                                wardKey.Url === `https://esgoo.net/api-tinhthanh/3/${_d.id}.htm`
                            )
                            .map(async (wardKey: any) => {
                              const wards = await this.mapApi(wardKey.Menthod, wardKey.Url, wardKey.Body);
  
                              await Promise.all(
                                wards.data.map(async (_w: any) => {
                                  const wardTemp: any = {};
                                  wardKey.MapSetting.forEach((_s: any) => {
                                    _s.convert
                                      ? (wardTemp[`${_s.key.match(/\$\.(.*)/)[1]}`] = toSecondsDateString(_w[`${_s.value}`]))
                                      : (wardTemp[`${_s.key.match(/\$\.(.*)/)[1]}`] = _w[`${_s.value}`]);
                                  });
  
                                  wardKey.AddField.forEach((_f: any) => {
                                    const gid = data.find((e: any) => e.Id === _f.value)?.GID;
                                    wardTemp[_f.key] = gid;
                                  });
                                  wardTemp["GID"] = randomGID();
                                  data.push({ ...wardTemp });
                                })
                              );
                            })
                        );
                      })
                    );
                  })
              );
            })
          );
        })
    );
  
    try {
      const dataConvert = data.map((e: any) => {
        e.Id = e.GID;
        delete e.GID;
        return {...e}
      })
      const results = await _dataRepo.action("add", dataConvert);
      return { data: results, code: 200, message: "Success" };
    } catch (err) {
      return { code: 404, message: (err as any).message ?? "" };
    }
  }
  
  
  async action2(action: string) {
    const _moduleRepo = new crudDA(`wini:Intergration:setting`);
    // const listProvinces = await this.mapApi("GET", "https://esgoo.net/api-tinhthanh/4/0.htm", "");
    const temp = {
      Name: "API lấy thông tin Tỉnh Thành, Quận Huyện, Phường Xã Việt Nam",
      Headers:"",
      Body:"", 
      Query:"", 
      Params:"", 
      Menthod:"GET",
      Url:"https://esgoo.net/api-tinhthanh/4/0.htm", 
      MapSetting:[
          {
              key:"$.Id",
              value:"id",
              convert: false
          },
          {
              key:"$.Name",
              value:"name",
              convert: false
          },
          {
              key:"$.NameAscii",
              value:"name_en",
              convert: false
          },
          {
              key:"$.FullName",
              value:"full_name",
              convert: false
          },
          {
              key:"$.FullNameAscii",
              value:"full_name_en",
              convert: false
          },
          {
              key:"$.Latitude",
              value:"latitude",
              convert: false
          },
          {
              key:"$.Longitude",
              value:"longitude",
              convert: false
          },
          {
            key:"$.Code",
            value:"id",
            convert: false
        },
      ],
      Description:"Api lấy tất cả tỉnh thành (63 tỉnh thành)",
      Type: 4,
      Data:""
    };

    const _settingProvinces: any = [{
      Id: randomGID(),
      ...temp
    }];
    await _moduleRepo.action('add', _settingProvinces);
  };

  async integration2() {
    // const _dataRepo = new crudDA(`tinhthanh:data`);
    const _settingRepo = new crudDA(`wini:Intergration:setting`);
    const _provinceRepo = new crudDA(`data:944b6a5577524e1eadef54ab63598daa:Province`);
    const _districtRepo = new crudDA(`data:944b6a5577524e1eadef54ab63598daa:District`);
    const _wardRepo = new crudDA(`data:944b6a5577524e1eadef54ab63598daa:Ward`);
  
    const listKeys = await _settingRepo.getAll();
    const data: Array<any> = [];
    const listProvince: Array<any> = [];
    const listDistrict: Array<any> = [];
    const listWard: Array<any> = [];
    // Xử lý các provinces
    await Promise.all(
      listKeys
        .filter((key: any) => key.Type === 4)
        .map(async (key: any) => {
          const provinces = await this.mapApi(key.Menthod, key.Url, key.Body);

            provinces.data.map(async (_p: any) => {
              const provinceTemp: any = {};
              key.MapSetting.forEach((_s: any) => {
                _s.convert
                  ? (provinceTemp[`${_s.key.match(/\$\.(.*)/)[1]}`] = toSecondsDateString(_p[`${_s.value}`]))
                  : (provinceTemp[`${_s.key.match(/\$\.(.*)/)[1]}`] = _p[`${_s.value}`]);
              });
              provinceTemp["Id"] = randomGID();
              provinceTemp["CountryId"] = "18258c9640594cad8ba346bf19b353e8";
              provinceTemp["Latitude"] = parseFloat(provinceTemp["Latitude"])  
              provinceTemp["Longitude"] = parseFloat(provinceTemp["Longitude"])  
              provinceTemp["Code"] = String(provinceTemp["Code"])  
              // _provinceRepo.action('add', provinceTemp);
              listProvince.push({...provinceTemp})
              // provinceTemp["Data2"] = provinceTemp["Data2"] || [];

              _p.data2.map(async (_d: any) => {
                const districtTemp: any = {};
                key.MapSetting.forEach((_s: any) => {
                  _s.convert
                    ? (districtTemp[`${_s.key.match(/\$\.(.*)/)[1]}`] = toSecondsDateString(_d[`${_s.value}`]))
                    : (districtTemp[`${_s.key.match(/\$\.(.*)/)[1]}`] = _d[`${_s.value}`]);
                });       
                districtTemp["Id"] = randomGID();
                districtTemp["Latitude"] = parseFloat(districtTemp["Latitude"])  
                districtTemp["Longitude"] = parseFloat(districtTemp["Longitude"])  
                districtTemp["Code"] = String(districtTemp["Code"]) 
                districtTemp["ProvinceId"] = provinceTemp.Id
                listDistrict.push({...districtTemp})
                
                // districtTemp["Data3"] = districtTemp["Data3"] || [];
                _d.data3.map((_w: any) => {
                  const wardTemp: any = {};
                  key.MapSetting.forEach((_s: any) => {
                    _s.convert
                      ? (wardTemp[`${_s.key.match(/\$\.(.*)/)[1]}`] = toSecondsDateString(_w[`${_s.value}`]))
                      : (wardTemp[`${_s.key.match(/\$\.(.*)/)[1]}`] = _w[`${_s.value}`]);
                  });       
                  wardTemp["Id"] = randomGID();
                  wardTemp["Latitude"] = parseFloat(wardTemp["Latitude"])  
                  wardTemp["Longitude"] = parseFloat(wardTemp["Longitude"])  
                  wardTemp["Code"] = String(wardTemp["Code"])
                  wardTemp["DistrictId"] = districtTemp.Id
                  // _wardRepo.action('add', wardTemp);
                  listWard.push({...wardTemp})
                  // districtTemp["Data3"].push({ ...wardTemp });
                })
                // _districtRepo.action('add', districtTemp);
                // provinceTemp["Data2"].push({ ...districtTemp });
              })
              data.push({ ...provinceTemp });
          });
        })
      );
  
    try {
      // const results = await _dataRepo.action("add", data);
      await _provinceRepo.action("add", listProvince);
      await _districtRepo.action("add", listDistrict);
      await _wardRepo.action("add", listWard);
      return { data: "ok", code: 200, message: "Success" };
    } catch (err) {
      return { code: 404, message: (err as any).message ?? "" };
    }
  }
  
  async deleteData(keys: string) {

  }
}
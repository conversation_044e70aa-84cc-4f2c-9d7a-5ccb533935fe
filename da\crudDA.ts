import { SearchOptions } from "redis";
import { redis } from "../index";

export default class crudDA {
  private schemaName: string;
  constructor(schemaName: string) {
    this.schemaName = schemaName;
  }

  async action(action: "add" | "edit" | "delete", data: Array<any>) {
    var list: any = [];
    await Promise.all(
      data.map((item: any) => {
        list.push({ id: item.Id, key: this.schemaName + ":" + item.Id, value: JSON.stringify([item]) });
        // return this.repo.save(item.Id, item);
        if (action === "edit") {
          return redis.json.merge(`${this.schemaName}:${item.Id}`, "$", item);
        } else {
          return redis.json.set(`${this.schemaName}:${item.Id}`, "$", item);
        }
      })
    );
    this.actionDB(list, action);
  }

  async eval(script: string, options?: { keys?: Array<string>; arguments?: Array<string> }) {
    redis.eval(script, options);
  }

  async countItem() {
    const _count = await redis.keys(`${this.schemaName}:*`);
    return _count.length;
  }

  async getBylistId(ids: Array<string>) {
    const result = await Promise.all(ids.map((id) => redis.json.get(`${this.schemaName}:${id}`)));
    return result;
  }

  async getById(id: string) {
    const result = await redis.json.get(`${this.schemaName}:${id}`);
    return result;
  }

  async getAll() {
    const keys = await redis.keys(`${this.schemaName}:*`);
    const result = await Promise.all(keys.map((id) => redis.json.get(id)));
    return result;
  }

  private actionDB(data: Array<any>, action: string) {
    // post(`${apiDB}/Action?action=${action}`, data);
  }

  // Xóa các khóa từ Redis
  async delete(ids: Array<string>) {
    await Promise.all(ids.map((id) => redis.json.del(`${this.schemaName}:${id}`)));
    // this.actionDB(ids, "delete");
  }

  async deleteAll() {
    const keys = await redis.keys(`${this.schemaName}:*`);
    await Promise.all(keys.map((id) => redis.json.del(id)));
    // this.actionDB(keys.map(id => id.replace(`${this.schemaName}:`, '')), "delete");
  }

  // Thêm dữ liệu với thời gian hết hạn vào Redis
  async expire(data: Array<{ id: string; seconds: number }>) {
    await Promise.all(data.map((item) => redis.expire(`${this.schemaName}:${item.id}`, item.seconds)));
  }

  async exist(id: string) {
    const item = await redis.exists(`${this.schemaName}:${id}`);
    return item;
  }

  async getKey(key: string) {
    const result = await redis.json.get(key);
    return result;
  }
  /**
   * AND: @field1:{value} @field2:[value value]
   * OR: @field1:{value} | @field2:[value value]
   * NOT: -@field1:{value}
   * OPTIONAL: ~@field1:{value}
   * ******
   * TEXT: search allMatch: @field:word, search inludes character or word: @field:word*
   * STRING: search allMatch: @field:{word}, search inludes character or word: @field:{word*}
   * NUMBER: search equal: @field:[number number], search in range: @field:[min max], search >: @field:[number +inf], search <: @field:[-inf number]
   * BOOLEAN: @field:{true|false}
   * tring[]: @field:{word}
   * NOSTEM - Text attributes can have the NOSTEM argument that disables stemming when indexing its values. This may be ideal for things like proper names.( ... TEXT NOSTEM weight 0.5 ....)
   *  */
  /*
      VERBATIM?: Tìm kiếm từ chính xác, không cố gắng mở rộng hay sửa đổi các từ khóa trong truy vấn.
      NOSTOPWORDS?: Bỏ qua các từ ngữ phổ biến không mang nhiều ý nghĩa như "and", "or", "the" trong quá trình tìm kiếm. ???
      WITHSORTKEYS?: Bao gồm các khóa sắp xếp trong kết quả tìm kiếm. ???
      INKEYS?: Giới hạn tìm kiếm trong các khóa cụ thể, có thể là một chuỗi hoặc một mảng các chuỗi. ???
      INFIELDS?: Giới hạn tìm kiếm trong các trường cụ thể, có thể là một chuỗi hoặc một mảng các chuỗi. ???
      RETURN?: Chỉ định các trường cần trả về trong kết quả, có thể là một chuỗi hoặc một mảng các chuỗi.
      SUMMARIZE?: true ( tóm tắt theo cấu hình mặc định ) | {
          FIELDS?: Các trường cần tóm tắt.
          FRAGS?:  Số đoạn tóm tắt cần lấy.
          LEN?:  Độ dài mỗi đoạn tóm tắt.
          SEPARATOR?: Ký tự phân cách giữa các đoạn tóm tắt.
      }; ( tự cấu hình )
      HIGHLIGHT?: true ( đánh dấu từ khoá tìm kiếm "Bôi đậm từ khoá tìm kiếm") | {
          FIELDS?: Các trường cần đánh dấu.
          TAGS?: ( Các thẻ HTML mở và đóng để đánh dấu từ khóa ) {
              open: '<strong>'; ( Thẻ mở để đánh dấu từ khóa )
              close: '</strong>; ( Thẻ đóng để kết thúc đánh dấu từ khóa )
          };
      };
      SLOP?: Cho phép số lượng từ nằm giữa các từ khóa trong câu tìm kiếm. (ví dụ: SLOP: 1 (tìm kiếm từ khoá "Vũ Chiến" -> kết quả có thể trả ra là "Vũ Đức Chiến" hoặc "Vũ Đình Chiến"))
      INORDER?: Bắt buộc các từ khóa phải xuất hiện theo thứ tự trong câu tìm kiếm. ???
      LANGUAGE?: Định nghĩa ngôn ngữ sử dụng cho tìm kiếm.
      EXPANDER?: Bộ mở rộng từ khóa để cải thiện tìm kiếm. ???
      SCORER?: Bộ đánh giá điểm để sắp xếp kết quả tìm kiếm.
      SORTBY?: Thuộc tính để sắp xếp kết quả tìm kiếm.
      LIMIT?: Giới hạn số lượng kết quả tìm kiếm trả về.
      {
          from: Bắt đầu từ kết quả nào. ( 0 )
          size:  Số lượng kết quả trả về. ( 10 )
      }; // lấy từ 0 lấy 10 phần tử
      PARAMS?: Tham số tùy chỉnh để sử dụng trong truy vấn. ???
      DIALECT?: Phiên bản cú pháp của ngôn ngữ truy vấn sử dụng. ??? (DIALECT: 2)
      TIMEOUT?: Thời gian tối đa để hoàn thành truy vấn, tính bằng mili giây. (TIMEOUT: 5000 "Cho phép 5s")
  */
  async search(page?: number, size?: number, query = "", options?: SearchOptions | undefined) {
    try {
      let _options: SearchOptions = { SORTBY: { BY: "DateCreated", DIRECTION: "DESC" }, ...(options ?? {}) };
      if (page && size)
        _options.LIMIT = {
          from: (page - 1) * size,
          size: size,
        };
      else _options.LIMIT = { from: 0, size: 10000 };
      const result = await redis.ft.search(`idx:${this.schemaName}`, query, {
        ..._options,
        DIALECT: 2,
      });
      return {
        data: result.documents.map((e) => e.value) as Array<any>,
        count: result.total,
      };
    } catch (error: any) {
      return { code: 404, message: error?.message ?? "search failed" };
    }
  }

  /**
   * Ex:  LOAD: ['@field'],
       STEPS: [
        {
          type: AggregateSteps.GROUPBY,
         properties: ['@field'],
         REDUCE: [{
            type: AggregateGroupByReducers.COUNT,
            AS: 'name'
         }]
        }
      ]
   *  
    Command Raw : ft.aggregate data:FootBall:OrderDetail:index "*" GROUPBY 1 @OrderId REDUCE COUNT 0 as _count REDUCE SUM 1 @Price as _totalPrice 
    index: index tìm kiếm
    query : điều kiện lọc (tương tự WHERE '*': lấy all hoặc '@Status:[1 1]: lấy theo trường cụ thể')
    STEPS : [{...}, {...}] chạy tuần tự từng lệnh 
    groupby : {type: goupby, properties: group theo các trường (propety : group theo 1 trường duy nhất - trả ra 1 bản ghi): REDUCE: {type: AggregateGroupByReducers.COUNT (chứa các hàm để tính toán (count, avg, sum, tolist,....) riêng COUNT thì không cần property), property: trường cần tính toán, AS: tên alias của trường}}
    apply: AggregateSteps.APPLY thường app dụng các hàm time vào để goup - chưa sử dụng được
    */

  /*
  FT.AGGREGATE idx:data:FootBall:OrderDetail "*" GROUPBY 1 @OrderId REDUCE COUNT 0 AS _count REDUCE SUM 1 @Price as _totalPrice
  APPLY "@_totalPrice / @_count" AS _avgPrice FILTER "@_avgPrice < 1000000" LIMIT 0 100
  */
  async aggregate(params: { query: string; searchRaw?: string }) {
    // const result = await redis.sendCommand([`FT.AGGREGATE idx:${this.schemaName} ${query} ${options}`]);
    // try {
    //   // Tách chuỗi thành mảng các phần tử
    //   //const command = query.split(' ');
    //   const cmd = `FT.AGGREGATE idx:${this.schemaName} ${params.searchRaw ?? "*"} ${params.query}`.match(/("[^"]+"|\[[^\]]+\]|\S+)/g) as RegExpMatchArray;
    //   // Gửi lệnh Redis bằng sendCommand
    //   const s = cmd.map(arg => arg.replace(/^"(.+(?="$))"$/, '$1'));
    //   const result = await redis.sendCommand(s);
    //   return result as any
    // } catch (error: any) {
    //   return { code: 404, message: error?.message ?? "aggregate failed" }
    // }
    try {
      // Sử dụng regex đã được chỉnh sửa để tách query
      const cmd = params.query.match(/("[^"]+"|\[[^\]]+\]|\{[^\}]+\}|\([^\)]+\)|\S+)/g) as RegExpMatchArray;

      // Loại bỏ dấu ngoặc kép dư thừa
      const s = cmd.map((arg) => arg.replace(/^"(.+(?="$))"$/, "$1"));
      // const rawCommand = params.searchRaw ? params.searchRaw.split(" @").map(e => e.startsWith("@") ? e : `@${e}`) : ["*"]

      // Gửi lệnh Redis với chuỗi đã được xử lý
      const redisCommand = [`FT.AGGREGATE`, `idx:${this.schemaName}`, params.searchRaw ?? "*", ...s, "DIALECT", "2"];
      const result = await redis.sendCommand(redisCommand);

      return result as any;
    } catch (error: any) {
      return { code: 404, message: error?.message ?? "aggregate failed" };
    }
  }
}

export const regexEmptyKeyController = /@([^:]+):(?:{empty}|{notempty})/;
export const replaceEmptyKeyController = /@([^:]+):(?:{empty}|{notempty})/g;
export const regexRelativeKeyController = /(?:-?@)?(?:\([^()]*\)|\w+Id\.\w+:(?:(?:\{[^}]*\})|(?:\[[^\]]*\])|(?:\([^\)]*\))|[^:)\]\}]+))/;
export const replaceRelativeKeyController = /(?:-?@)?(?:\([^()]*\)|\w+Id\.\w+:(?:(?:\{[^}]*\})|(?:\[[^\]]*\])|(?:\([^\)]*\))|[^:)\]\}]+))/g;

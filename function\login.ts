import { JWKS_URI, JWT_SECRET } from "./../config";
import { OAuth2Client, LoginTicket } from "google-auth-library";
import jwksClient, { SigningKey } from "jwks-rsa";
import jwt, { JwtPayload } from "jsonwebtoken";
import bcrypt from "bcrypt";

export default class login {
  private appleclient: jwksClient.JwksClient;

  constructor() {
    this.appleclient = jwksClient({ jwksUri: JWKS_URI });
  }

  //apple
  getAppleSigningKey(kid: string): Promise<string> {
    return new Promise((resolve, reject) => {
      this.appleclient.getSigningKey(kid, (err, key: SigningKey | undefined) => {
        if (err) {
          return reject(err);
        }
        resolve(key?.getPublicKey() as string);
      });
    });
  }

  verifyJWT(json: string, publicKey: string): Promise<JwtPayload> {
    return new Promise((resolve, reject) => {
      jwt.verify(json, publicKey, (err, payload) => {
        if (err) {
          return reject(err);
        }
        resolve(payload as JwtPayload);
      });
    });
  }

  // google
  async verifyGoogleLogin(googleClientId: string, token: string, secretKey: string): Promise<JwtPayload | null> {
    const _googleClient = new OAuth2Client({
      clientId: googleClientId,
      clientSecret: secretKey,
      redirectUri: "postmessage",
    });
    try {
      const { tokens } = await _googleClient.getToken(token); // exchange code for tokens
      var id_token = tokens.id_token as string;
    } catch (err) {
      id_token = token;
    }
    try {
      const ticket: LoginTicket = await _googleClient.verifyIdToken({
        audience: googleClientId,
        idToken: id_token,
      });
      const payload: JwtPayload | undefined = ticket.getPayload();
      return payload || null;
    } catch (error: any) {
      console.error("Error verifying Google login:", error);
      return { code: 404, message: error.message } as any;
    }
  }

  // token app
  createToken(payload: object, secret: string, expiresIn: string) {
    return jwt.sign(payload, secret, { expiresIn: expiresIn as jwt.SignOptions["expiresIn"] });
  }

  createAccessToken(payload: object) {
    return this.createToken(payload, JWT_SECRET, "10m");
  }

  createRefreshToken(payload: object) {
    return this.createToken(payload, JWT_SECRET, "30d");
  }

  verifyToken(token: string) {
    try {
      return jwt.verify(token, JWT_SECRET);
    } catch (error) {
      throw error;
    }
  }

  async hashPassword(password: string): Promise<string> {
    const saltRounds = 10; // Số vòng lặp để tạo muối
    const salt = await bcrypt.genSalt(saltRounds);
    return await bcrypt.hash(password, salt);
  }

  // Hàm kiểm tra mật khẩu khi đăng nhập
  async verifyPassword(password: string, hashedPassword: string): Promise<boolean> {
    return await bcrypt.compare(password, hashedPassword);
  }

  async refreshToken(refreshToken: string) {
    if (!refreshToken) {
      return { code: 400, message: "Refresh token is missing" };
    }
    try {
      const payload = this.verifyToken(refreshToken);
      if (typeof payload === "object" && payload !== null) {
        const newAccessToken = this.createAccessToken({ id: payload.id, email: payload.email });
        return { code: 200, accessToken: newAccessToken };
      } else {
        return { code: 401, message: "Invalid refresh token" };
      }
    } catch (error) {
      return { code: 401, message: "Invalid refresh token" };
    }
  }
}

import express from "express";
import cors from "cors";
import { createServer } from "http";
import { Server } from "socket.io";
import { createClient } from "redis";
import { REDIS_HOST, REDIS_PORT } from "./config";
import { setupSwagger } from "./swagger";

const app = express();
// Khởi động Swagger
setupSwagger(app);
const port = process.env.PORT || 3000;
var server = createServer(app);
var io = new Server(server, {
  cors: { origin: "*" },
  allowEIO3: true,
});

app.use(express.json({ limit: "50mb" }));

app.use(
  cors({
    origin: "*",
  })
);

export const redis = createClient({
  socket: {
    host: REDIS_HOST,
    port: REDIS_PORT,
  },
  // password: "81951ebfb79a444882431419cc4c66a0",
});
redis.connect();
//
// load initModuleIndex function
import dataRouter from "./router/data";
import integrationRouter from "./router/intergration";
import uploadFileRoute from "./router/uploadFile";
import vnPayRouter from "./router/payment";
import googleAuthen from "./router/googleAuthen";
app.use("/api/data", dataRouter);
app.use("/api/intergration", integrationRouter);
app.use("/api/file", uploadFileRoute);
app.use("/api/vnpay", vnPayRouter);
app.use("/api/2fa", googleAuthen);

// process.env.PORT
server.listen(port, () => {
  console.log(`Server is running on http://localhost:${port}`);
  console.log(`Swagger docs available at http://localhost:${port}/api-docs`);
});

interface User {
  id: string;
  name: string;
}

let onlineUsers: User[] = [];
const userRooms = new Map();
io.on("connection", function (socket) {
  console.log("ID ket noi server: " + socket.id);
  console.log("onlineUsers: " + onlineUsers.length);
  const { CustomerId, Name } = socket.handshake.auth;
  console.log("CustomerId: " + CustomerId);
  onlineUsers.push({ id: CustomerId, name: Name });
  socket.emit("onlineUsers", onlineUsers);
  socket.broadcast.emit("user-online", CustomerId);
  //#region Chat Application
  socket.on("join-rooms", (roomIds) => {
    if (!userRooms.has(CustomerId)) userRooms.set(CustomerId, new Set());
    roomIds.forEach((roomId: any) => {
      socket.join(roomId);
      userRooms.get(CustomerId).add(roomId);
      io.to(roomId).emit("user-join-room", { CustomerId: CustomerId, name: Name });
    });
  });
  // Gửi tin nhắn
  socket.on("send-message", ({ roomId, message }) => {
    io.to(roomId).emit("receive-message", {
      roomId,
      fromUserId: CustomerId,
      message
    });
  });
  // Typing
  socket.on("typing", ({ roomId }) => {
    socket.to(roomId).emit("typing", {
      roomId,
      fromUserId: CustomerId
    });
  });
  //#endregion




  //#region Call Application
  socket.on("candidate", ({ candidate, targetUserId }) => {
    io.to(targetUserId).emit("candidate", candidate);
  });


  socket.on("call-user", (data) => {
    io.to(data.targetUserId).emit("incoming-call", { from: socket.id });
  });
  socket.on("accept-call", (data) => {
    io.to(data.from).emit("accept-call");
  });
  socket.on("reject-call", (data) => {
    io.to(data.from).emit("reject-call");
  });
  socket.on("offer", (data) => {
    io.to(data.targetUserId).emit("offer", { offer: data.offer });
  });
  socket.on("answer", (data) => {
    io.to(data.id).emit("answer", data);
  });
  //#endregion
  //#region Disconnect
  socket.on("disconnect", async function () {
    console.log("disconnect " + socket.id);
    onlineUsers = onlineUsers.filter(user => user.id !== CustomerId);
    socket.broadcast.emit("user-offline", CustomerId);
  });
  //#endregion
});

import fs from "fs";
import axios from "axios";
import path from "path";
import { Router } from "express";
import multer, { StorageEngine, Multer } from "multer";
import crudDA from "../da/crudDA";
import { randomGID } from "../Ultis/convert";

interface FileData {
  Id: string;
  Name: string;
  Type: string;
  Url: string;
  Size: number;
}

const router = Router();
const dateTime = new Date();
const year = dateTime.getFullYear();
const month = dateTime.getMonth() + 1;

const storage: StorageEngine = multer.diskStorage({
  destination(req, file, cb) {
    const _pid = req.headers["pid"];
    const _folder = "./uploads";
    let url = _folder + "/" + _pid;
    if (!fs.existsSync(url)) fs.mkdirSync(url);
    url += "/" + year;
    if (!fs.existsSync(url)) fs.mkdirSync(url);
    url += "/" + month;
    if (!fs.existsSync(url)) fs.mkdirSync(url);
    cb(null, url);
  },
  filename(req, file, cb) {
    cb(null, file.originalname);
  },
});
const upload: Multer = multer({ storage: storage });

/**
 * @swagger
 * /file/uploadfiles:
 *   post:
 *     summary: Upload multiple files
 *     tags: [File]
 *     requestBody:
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               files:
 *                 type: array
 *                 items:
 *                   type: string
 *                   format: binary
 *                 description: List of files to upload (max 12 files)
 *     parameters:
 *       - in: header
 *         name: pid
 *         schema:
 *           type: string
 *         required: true
 *         description: Project ID
 *     responses:
 *       200:
 *         description: Files uploaded successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       Id:
 *                         type: string
 *                       Name:
 *                         type: string
 *                       Type:
 *                         type: string
 *                       Url:
 *                         type: string
 *                       Size:
 *                         type: integer
 *                 message:
 *                   type: string
 *                   example: Success
 *       404:
 *         description: Error occurred during upload
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 404
 *                 message:
 *                   type: string
 */
router.post("/uploadfiles", upload.array("files", 12), async (req, res, next) => {
  try {
    const { pid } = req.headers;
    const _imgRepo = new crudDA("img");
    var listfiles: Array<FileData> = [];
    if (!req.files) {
      res.json("");
      res.end();
      return;
    }
    (req.files as [])?.map((item: any) => {
      listfiles.push({
        Id: randomGID(),
        Name: item.filename,
        Type: item.type ?? item.mimetype,
        Url: `/uploads/${pid}/${year}/${month}/${item.filename}`,
        Size: item.size,
      } as FileData);
    });

    await _imgRepo.action("add", listfiles);
    return res.status(200).json({ code: 200, data: listfiles, message: "Success" });
  } catch (error) {
    return res.status(404).json({ code: 404, message: error });
  }
});

router.post("/editFileInfor", async (req, res) => {
  const _imgRepo = new crudDA("img");
  const { data } = req.body;
  if (!data) return res.send({ code: 404, message: "Data is empty" });

  if (data.length) {
    await _imgRepo.action(
      "edit",
      data.map((e: any) => {
        const tmp = { ...e };
        delete tmp.Type;
        delete tmp.Url;
        delete tmp.Size;
        return tmp;
      })
    );
  }
  return res.send({ code: 200, message: "Success" });
});

/**
 * @swagger
 * /file/getFilesInfor:
 *   post:
 *     summary: Get information of files by IDs
 *     tags: [File]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               ids:
 *                 type: array
 *                 items:
 *                   type: string
 *                 description: List of file IDs
 *             required:
 *               - ids
 *     responses:
 *       200:
 *         description: File information retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                 message:
 *                   type: string
 *                   example: Success
 */
router.post("/getFilesInfor", async (req, res) => {
  const _imgRepo = new crudDA("img");
  const { ids } = req.body;

  const files = await _imgRepo.getBylistId(ids);
  return res.send({ code: 200, data: files, message: "Success" });
});

/**
 * @swagger
 * /file/img/{id}:
 *   get:
 *     summary: Retrieve a file by ID
 *     tags: [File]
 *     parameters:
 *       - in: path
 *         name: id
 *         schema:
 *           type: string
 *         required: true
 *         description: File ID (32 or 36 characters)
 *     responses:
 *       200:
 *         description: File retrieved successfully
 *         content:
 *           image/*:
 *             schema:
 *               type: string
 *               format: binary
 *           image/svg+xml:
 *             schema:
 *               type: string
 *           application/octet-stream:
 *             schema:
 *               type: string
 *               format: binary
 *       404:
 *         description: File not found
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: File not found
 */
router.get("/img/:id", async (req, res) => {
  if (req.params.id?.length === 32) {
    const _imgRepo = new crudDA(`img`);
    const { id } = req.params;

    const img = ((await _imgRepo.getById(id)) as any).Url;
    if (!img) {
      throw new Error("File not found or URL is missing");
    }
    const filePath = path.join(__dirname, `../${img}`);

    fs.readFile(filePath, (err, data) => {
      if (err) {
        return res.status(404).json({ message: "File not found" });
      }
      res.setHeader("Content-Type", "application/octet-stream");
      const fileContent = data.toString("utf8").trim();
      if (fileContent.startsWith("<?xml") || fileContent.includes("<svg")) {
        res.setHeader("Content-Type", "image/svg+xml");
        return res.send(fileContent);
      } else res.send(data);
    });
  } else if (req.params.id?.length === 36) {
    const fileUrl = `https://file-mamager.wini.vn/api/SystemFile/img/${req.params.id}`;
    try {
      const response = await axios({
        url: fileUrl,
        method: "GET",
        responseType: "stream",
      });
      let contentType = getMimeTypeFromExtension(path.extname(response.headers["content-disposition"].toLowerCase()));

      if (contentType) {
        res.setHeader("Content-Type", contentType);
        if (isSupportedImage(contentType)) {
          res.setHeader("Content-Disposition", "inline; filename=image" + contentType);
        }
        response.data.pipe(res);
      } else {
        // Set headers for file download
        res.setHeader("Content-Type", response.headers["content-type"] || "application/octet-stream");
        res.setHeader("Content-Disposition", response.headers["content-disposition"]);

        // Pipe the Axios response stream directly to the client
        response.data.pipe(res);
      }
    } catch (error) {
      return res.status(404).json({ message: error });
    }
  } else return res.status(404).json({ message: "File not found!" });
});

/**
 * @swagger
 * /file/icon-library:
 *   get:
 *     summary: Get icon library
 *     tags: [File]
 *     parameters:
 *       - in: query
 *         name: type
 *         schema:
 *           type: string
 *           enum: [color, fill, outline]
 *         description: Type of icons to retrieve
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: Search term for filtering icons
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: Page number for pagination
 *     responses:
 *       200:
 *         description: List of icons retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 type: string
 */
router.get("/icon-library", function (req, res) {
  const _dir = `D:/Code/icon-library/`;
  const { type, search, page } = req.query;
  const _page: number = page ? (typeof page === "number" ? page : parseInt(`${page}`)) : 1;
  function searchIcons(list: Array<string>, type?: "color" | "fill" | "outline") {
    if (`${search ?? ""}`?.length)
      return list.filter((item: string) =>
        item
          .replace(_dir + (type ? type + "/" : ""), "")
          .toLowerCase()
          .includes(`${search ?? ""}`.toLowerCase())
      );
    else return list;
  }
  switch (type) {
    case "color":
      const colorIcons = searchIcons(getFiles(_dir + "color"), "color")
        .slice((_page - 1) * 20, _page * 20)
        .map((e) => e.replace(_dir, ""));
      return res.status(200).json(colorIcons);
    case "fill":
      const fillIcons = searchIcons(getFiles(_dir + "fill"), "fill")
        .slice((_page - 1) * 20, _page * 20)
        .map((e) => e.replace(_dir, ""));
      return res.status(200).json(fillIcons);
    case "outline":
      const outlineIcons = searchIcons(getFiles(_dir + "outline"), "outline")
        .slice((_page - 1) * 20, _page * 20)
        .map((e) => e.replace(_dir, ""));
      return res.status(200).json(outlineIcons);
    default:
      const icons = searchIcons([...getFiles(_dir + "color"), ...getFiles(_dir + "fill"), ...getFiles(_dir + "outline")])
        .slice((_page - 1) * 20, _page * 20)
        .map((e) => e.replace(_dir, ""));
      return res.status(200).json(icons);
  }
});

// Helper function to validate supported image formats
function isSupportedImage(mimeType: string) {
  const supportedTypes = ["image/jpeg", "image/png", "image/gif", "image/svg+xml"];
  return supportedTypes.includes(mimeType);
}

function getMimeTypeFromExtension(ext: string) {
  switch (ext) {
    case ".jpg":
    case ".jpeg":
      return "image/jpeg";
    case ".png":
      return "image/png";
    case ".gif":
      return "image/gif";
    case ".svg":
      return "image/svg+xml";
    default:
      return undefined;
  }
}

function getFiles(dir: string) {
  const files: Array<string> = [];
  const fileList = fs.readdirSync(dir); // Get an array of files and directories
  for (const file of fileList) {
    const name = `${dir}/${file}`;
    const stat = fs.statSync(name);
    if (stat.isDirectory()) {
      files.push(...getFiles(name)); // Recurse if directory
    } else {
      files.push(name.replace(".svg", "")); // Add file path and modified time
    }
  }
  return files; // Return the list of files with modified times
}

export default router;
